from flask import Blueprint, render_template, request, redirect, url_for, session
from models import Meme, db
from utils.helpers import require_login, get_user_config, save_meme_to_db
from modules.reddit_scraper import get_top_memes
import logging

memes_bp = Blueprint('memes', __name__)

@memes_bp.route('/fetch_memes')
@require_login
def fetch_memes():
    """Fetch memes from Reddit"""
    user_id = session['user_id']
    config = get_user_config(user_id)

    # Parse subreddits from config
    subreddits = [s.strip() for s in config.subreddits.split(',') if s.strip()]

    logging.info(f"Starting to fetch memes for user {user_id} from subreddits: {subreddits}")

    try:
        # Fetch memes from Reddit
        reddit_memes = get_top_memes(limit=config.max_memes, subreddits=subreddits)

        if not reddit_memes:
            logging.warning("No memes returned from Reddit API")
            session['flash_message'] = "No memes could be fetched from Reddit. Please check your internet connection and try again."
            return redirect(url_for('dashboard.dashboard'))

        logging.info(f"Successfully fetched {len(reddit_memes)} memes from Reddit")

        # Get existing meme URLs to avoid duplicates
        existing_urls = set(meme.url for meme in Meme.query.filter_by(user_id=user_id, discarded=False).all())

        new_memes_count = 0
        duplicate_count = 0
        error_count = 0

        for meme_info in reddit_memes:
            try:
                meme_url = meme_info.get('url', '')
                meme_title = meme_info.get('title', '')

                if not meme_url:
                    logging.warning(f"Skipping meme with no URL: {meme_info}")
                    error_count += 1
                    continue

                # Check for duplicates
                if meme_url in existing_urls:
                    logging.debug(f"Skipping duplicate meme: {meme_url}")
                    duplicate_count += 1
                    continue

                # Save new meme to database
                meme = save_meme_to_db(user_id, meme_url, "")
                existing_urls.add(meme_url)  # Add to set to prevent duplicates in this batch
                new_memes_count += 1

                logging.debug(f"Saved new meme: {meme_title} - {meme_url}")

            except Exception as e:
                logging.error(f"Error processing meme {meme_info}: {e}")
                error_count += 1
                continue

        # Log summary
        logging.info(f"Fetch complete - New: {new_memes_count}, Duplicates: {duplicate_count}, Errors: {error_count}")

        # Set flash message for user feedback
        if new_memes_count > 0:
            session['flash_message'] = f"Successfully fetched {new_memes_count} new memes!"
            if duplicate_count > 0:
                session['flash_message'] += f" ({duplicate_count} duplicates skipped)"
        elif duplicate_count > 0:
            session['flash_message'] = f"All {duplicate_count} memes were already in your collection."
        else:
            session['flash_message'] = "No new memes could be fetched. Please try again later."

    except Exception as e:
        logging.error(f"Critical error in fetch_memes: {e}")
        session['flash_message'] = f"Error fetching memes: {str(e)}"

    return redirect(url_for('dashboard.dashboard'))

@memes_bp.route('/clear_memes', methods=['POST'])
@require_login
def clear_memes():
    """Clear all existing memes for the user"""
    user_id = session['user_id']

    try:
        # Count memes before deletion
        meme_count = Meme.query.filter_by(user_id=user_id, discarded=False).count()

        # Clear existing memes for this user
        Meme.query.filter_by(user_id=user_id).delete()
        db.session.commit()

        logging.info(f"Cleared {meme_count} memes for user {user_id}")
        session['flash_message'] = f"Cleared {meme_count} memes from your collection."

    except Exception as e:
        logging.error(f"Error clearing memes for user {user_id}: {e}")
        session['flash_message'] = f"Error clearing memes: {str(e)}"

    return redirect(url_for('dashboard.dashboard'))

@memes_bp.route('/review_memes')
@require_login
def review_memes():
    """Review and edit memes"""
    user_id = session['user_id']
    memes = Meme.query.filter_by(user_id=user_id, discarded=False).all()
    return render_template('review_memes.html', memes=memes)

@memes_bp.route('/update_meme_text', methods=['POST'])
@require_login
def update_meme_text():
    """Update meme text"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')
    new_text = request.form.get('text', '')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.text = new_text
        db.session.commit()

    return redirect(url_for('memes.review_memes'))

@memes_bp.route('/discard_meme', methods=['POST'])
@require_login
def discard_meme():
    """Discard a meme"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.discarded = True
        db.session.commit()

    return redirect(url_for('memes.review_memes'))
