import praw
import os
import logging
from dotenv import load_dotenv

load_dotenv()  # loads variables from .env into environment

def get_top_memes(limit=10, subreddits=None):
    """
    Fetch top memes from specified subreddits

    Args:
        limit (int): Number of memes to fetch per subreddit
        subreddits (list): List of subreddit names to fetch from

    Returns:
        list: List of dictionaries containing meme data
    """
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    username = os.getenv('REDDIT_USERNAME')

    # Validate Reddit credentials
    if not all([client_id, client_secret, username]):
        logging.error("Missing Reddit API credentials. Please check your .env file.")
        return []

    try:
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=f'AutomaticMemeContentGenerator/0.1 by {username}'
        )

        # Test Reddit connection with a simple read-only operation
        test_subreddit = reddit.subreddit('memes')
        test_subreddit.display_name  # This will raise an exception if connection fails
        logging.info("Successfully connected to Reddit API")

    except Exception as e:
        logging.error(f"Failed to connect to Reddit API: {e}")
        return []

    memes = []
    if not subreddits:
        subreddits = ['memes']  # Default subreddit

    for subreddit_name in subreddits:
        try:
            logging.info(f"Fetching memes from r/{subreddit_name}")
            subreddit = reddit.subreddit(subreddit_name)

            # Verify subreddit exists and is accessible
            subreddit_info = subreddit.display_name
            logging.debug(f"Accessing subreddit: {subreddit_info}")

            meme_count = 0
            for submission in subreddit.top(time_filter='day', limit=limit):
                # Check if it's an image
                if submission.url.endswith(('jpg', 'jpeg', 'png', 'gif', 'webp')):
                    memes.append({
                        'url': submission.url,
                        'title': submission.title,
                        'subreddit': subreddit_name,
                        'score': submission.score,
                        'created_utc': submission.created_utc
                    })
                    meme_count += 1
                    logging.debug(f"Added meme: {submission.title[:50]}...")

            logging.info(f"Fetched {meme_count} memes from r/{subreddit_name}")

        except Exception as e:
            logging.error(f"Error fetching memes from subreddit r/{subreddit_name}: {e}")
            continue

    logging.info(f"Total memes fetched: {len(memes)}")
    return memes

if __name__ == '__main__':
    memes = get_top_memes()
    for m in memes:
        print(m['title'], m['url'])
