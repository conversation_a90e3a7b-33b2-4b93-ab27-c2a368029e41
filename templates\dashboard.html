<!DOCTYPE html>
<html>
<head>
    <title>Meme Content Generator</title>
    <link rel="stylesheet" href="/static/styles.css">
    <style>
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
        .stat-label {
            font-size: 1.1em;
            color: #6c757d;
            margin-bottom: 10px;
        }
        .stat-description {
            font-size: 0.9em;
            color: #868e96;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }
        .action-btn {
            display: block;
            padding: 15px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .action-btn:hover {
            background: #0056b3;
            color: white;
        }
        .action-btn.secondary {
            background: #6c757d;
        }
        .action-btn.secondary:hover {
            background: #545b62;
        }
        .action-btn.success {
            background: #28a745;
        }
        .action-btn.success:hover {
            background: #1e7e34;
        }
        .action-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .action-btn.warning:hover {
            background: #e0a800;
        }
        .stat-card.need-attention {
            border-left: 4px solid #ffc107;
        }
        .stat-card.success {
            border-left: 4px solid #28a745;
        }
        .stat-card.info {
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
        </div>
        {% endif %}

        <!-- Statistics Overview -->
        <h2>📊 Content Statistics</h2>
        <div class="stats-grid">
            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_memes }}</div>
                <div class="stat-label">Total Memes</div>
                <div class="stat-description">Memes fetched and ready</div>
            </div>

            <div class="stat-card {% if stats.memes_need_text > 0 %}need-attention{% else %}success{% endif %}">
                <div class="stat-number">{{ stats.memes_need_text }}</div>
                <div class="stat-label">Need Text</div>
                <div class="stat-description">Memes waiting for text</div>
            </div>

            <div class="stat-card success">
                <div class="stat-number">{{ stats.memes_with_text }}</div>
                <div class="stat-label">Ready for Videos</div>
                <div class="stat-description">Memes with text added</div>
            </div>

            <div class="stat-card info">
                <div class="stat-number">{{ stats.total_videos }}</div>
                <div class="stat-label">Videos Created</div>
                <div class="stat-description">Generated video content</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.memes_today }}</div>
                <div class="stat-label">Memes Today</div>
                <div class="stat-description">Fetched today</div>
            </div>

            <div class="stat-card">
                <div class="stat-number">{{ stats.videos_today }}</div>
                <div class="stat-label">Videos Today</div>
                <div class="stat-description">Created today</div>
            </div>
        </div>

        <!-- Quick Actions -->
        <h2>🚀 Quick Actions</h2>
        <div class="quick-actions">
            {% if stats.total_memes == 0 %}
                <a href="/fetch_memes" class="action-btn">🎭 Fetch Memes</a>
                <a href="/config" class="action-btn secondary">⚙️ Configure Settings</a>
            {% elif stats.memes_need_text > 0 %}
                <a href="/review_memes" class="action-btn warning">✏️ Add Text to {{ stats.memes_need_text }} Memes</a>
                <a href="/fetch_memes" class="action-btn secondary">🎭 Fetch More Memes</a>
            {% elif stats.memes_with_text > 0 and stats.total_videos == 0 %}
                <a href="/generate_videos" class="action-btn success">🎬 Generate Videos</a>
                <a href="/review_memes" class="action-btn secondary">✏️ Review Memes</a>
            {% else %}
                <a href="/review_videos" class="action-btn">📹 Review Videos</a>
                <a href="/generate_videos" class="action-btn success">🎬 Generate More Videos</a>
                <a href="/fetch_memes" class="action-btn secondary">🎭 Fetch More Memes</a>
            {% endif %}
            <a href="/config" class="action-btn secondary">⚙️ Settings</a>
            {% if stats.total_memes > 0 %}
            <form method="POST" action="/clear_memes" style="display: inline;">
                <button type="submit" class="action-btn" style="background: #dc3545;" onclick="return confirm('Are you sure you want to clear all memes? This cannot be undone.')">🗑️ Clear All Memes</button>
            </form>
            {% endif %}
        </div>

        <!-- Workflow Status -->
        {% if stats.total_memes > 0 %}
        <h2>📈 Workflow Progress</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <span><strong>Content Pipeline Status</strong></span>
                <span>{{ ((stats.total_videos / stats.total_memes) * 100) | round(1) if stats.total_memes > 0 else 0 }}% Complete</span>
            </div>
            <div style="background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden;">
                <div style="background: #28a745; height: 100%; width: {{ ((stats.total_videos / stats.total_memes) * 100) if stats.total_memes > 0 else 0 }}%; transition: width 0.3s;"></div>
            </div>
            <div style="margin-top: 10px; font-size: 0.9em; color: #6c757d;">
                {{ stats.total_memes }} memes → {{ stats.memes_with_text }} with text → {{ stats.total_videos }} videos
            </div>
        </div>
        {% endif %}

        <!-- Additional Stats -->
        {% if stats.discarded_memes > 0 or stats.discarded_videos > 0 %}
        <h2>🗑️ Discarded Content</h2>
        <div class="stats-grid">
            {% if stats.discarded_memes > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_memes }}</div>
                <div class="stat-label">Discarded Memes</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
            {% if stats.discarded_videos > 0 %}
            <div class="stat-card">
                <div class="stat-number">{{ stats.discarded_videos }}</div>
                <div class="stat-label">Discarded Videos</div>
                <div class="stat-description">Removed from workflow</div>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</body>
</html>
