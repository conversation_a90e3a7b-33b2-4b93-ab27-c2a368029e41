<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration</title>
    <link rel="stylesheet" href="/static/styles.css">
</head>
<body>
    <header>
        <h1>Configuration</h1>
    </header>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/fetch_memes">🎭 Fetch Memes</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <h2>Update Configuration</h2>
        <form method="POST" action="/update_config">
            <div class="form-group">
                <label for="max_memes">Max Memes (1-100):</label>
                <input type="number" id="max_memes" name="max_memes" min="1" max="100"
                       value="{{ config.max_memes }}" required>
            </div>

            <div class="form-group">
                <label for="subreddits">Subreddits:</label>
                <div id="subreddits">
                    {% for subreddit in available_subreddits %}
                    <div class="subreddit-item">
                        <input type="checkbox" id="subreddit_{{ subreddit }}" name="subreddits"
                               value="{{ subreddit }}" {% if subreddit in selected_subreddits %}checked{% endif %}>
                        <label for="subreddit_{{ subreddit }}">
                            <a href="https://reddit.com/r/{{ subreddit }}" target="_blank" class="subreddit-link">
                                r/{{ subreddit }}
                            </a>
                        </label>
                    </div>
                    {% endfor %}
                    <div class="add-subreddit">
                        <input type="text" id="new_subreddit" name="new_subreddit"
                               placeholder="Add subreddit name or URL (e.g., 'memes' or 'https://reddit.com/r/memes')">
                        <button type="button" onclick="addSubreddit()">Add</button>
                    </div>
                </div>
            </div>

            <button type="submit">Save Settings</button>
        </form>

        <div class="current-config">
            <h3>Current Configuration</h3>
            <p><strong>Max Memes:</strong> {{ config.max_memes }}</p>
            <p><strong>Selected Subreddits:</strong></p>
            <div class="current-subreddits">
                {% for subreddit in selected_subreddits %}
                <span class="subreddit-tag">
                    <a href="https://reddit.com/r/{{ subreddit }}" target="_blank" class="subreddit-link">
                        r/{{ subreddit }}
                    </a>
                </span>
                {% endfor %}
            </div>
        </div>
    </div>
    <script>
        function addSubreddit() {
            const newSubredditInput = document.getElementById('new_subreddit');
            const subredditList = document.getElementById('subreddits');

            if (newSubredditInput.value.trim() !== '') {
                const inputValue = newSubredditInput.value.trim();

                // Extract subreddit name from URL or use as-is
                let subredditName = inputValue;
                const urlMatch = inputValue.match(/(?:https?:\/\/)?(?:www\.)?reddit\.com\/r\/([a-zA-Z0-9_]+)/);
                if (urlMatch) {
                    subredditName = urlMatch[1];
                } else if (inputValue.startsWith('r/')) {
                    subredditName = inputValue.substring(2);
                }

                const newDiv = document.createElement('div');
                newDiv.className = 'subreddit-item';
                newDiv.innerHTML = `
                    <input type="checkbox" id="subreddit_${subredditName}" name="subreddits" value="${subredditName}" checked>
                    <label for="subreddit_${subredditName}">
                        <a href="https://reddit.com/r/${subredditName}" target="_blank" class="subreddit-link">
                            r/${subredditName}
                        </a>
                    </label>
                `;
                subredditList.insertBefore(newDiv, subredditList.lastElementChild);
                newSubredditInput.value = '';
            }
        }
    </script>
</body>
</html>
