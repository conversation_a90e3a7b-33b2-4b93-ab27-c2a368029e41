from flask import Blueprint, render_template, request, redirect, url_for, session
from models import db
from utils.helpers import require_login, get_user_config

config_bp = Blueprint('config', __name__)

@config_bp.route('/config')
@require_login
def config():
    """Configuration page"""
    user_id = session['user_id']
    config = get_user_config(user_id)

    available_subreddits = ['memes', 'dankmemes', 'wholesomememes', 'memeeconomy', 'prequelmemes']
    selected_subreddits = [s.strip() for s in config.subreddits.split(',') if s.strip()]

    return render_template('config.html',
                         config=config,
                         available_subreddits=available_subreddits,
                         selected_subreddits=selected_subreddits)

@config_bp.route('/update_config', methods=['POST'])
@require_login
def update_config():
    """Update user configuration"""
    user_id = session['user_id']
    config = get_user_config(user_id)

    # Update configuration
    config.max_memes = min(100, max(1, int(request.form.get('max_memes', 10))))

    # Handle subreddits
    selected_subreddits = request.form.getlist('subreddits')
    new_subreddit = request.form.get('new_subreddit', '').strip()
    if new_subreddit:
        selected_subreddits.append(new_subreddit)

    config.subreddits = ','.join(selected_subreddits) if selected_subreddits else 'memes'

    db.session.commit()

    return redirect(url_for('config.config'))
