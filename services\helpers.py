from functools import wraps
from flask import session, redirect, url_for
from models import Configuration, Meme, db

def require_login(f):
    """Decorator to require user login for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

def get_user_config(user_id):
    """Get user configuration or create default"""
    config = Configuration.query.filter_by(user_id=user_id).first()
    if not config:
        config = Configuration(user_id=user_id)
        db.session.add(config)
        db.session.commit()
    return config

def save_meme_to_db(user_id, url, text=""):
    """Save meme to database"""
    meme = Meme(user_id=user_id, url=url, text=text)
    db.session.add(meme)
    db.session.commit()
    return meme
