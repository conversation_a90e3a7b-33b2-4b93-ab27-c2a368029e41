import requests
import tempfile
import os
import logging

# Import video generation libraries
try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False

def create_video_with_opencv(meme, audio_path, output_path):
    """Create video using OpenCV (simpler approach)"""
    try:
        # Download image
        response = requests.get(meme.url, timeout=10)
        response.raise_for_status()

        # Save image temporarily
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_img:
            temp_img.write(response.content)
            temp_img_path = temp_img.name

        # Load and resize image
        img = cv2.imread(temp_img_path)
        height, width, _ = img.shape

        # Resize to standard video size
        target_height = 720
        target_width = int(width * (target_height / height))
        img_resized = cv2.resize(img, (target_width, target_height))

        # Get audio duration (approximate from file size)
        audio_duration = 5.0  # Default 5 seconds, could be improved
        if os.path.exists(audio_path):
            # Simple estimation based on file size (very rough)
            file_size = os.path.getsize(audio_path)
            audio_duration = max(3.0, min(10.0, file_size / 10000))  # Rough estimate

        # Create video writer
        fps = 1  # 1 frame per second for static image
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(output_path, fourcc, fps, (target_width, target_height))

        # Write frames (static image for duration)
        total_frames = int(audio_duration * fps)
        for _ in range(total_frames):
            video_writer.write(img_resized)

        # Release video writer
        video_writer.release()

        # Cleanup
        os.unlink(temp_img_path)

        logging.info(f"Created video with OpenCV: {output_path}")
        return True

    except Exception as e:
        logging.error(f"Error creating video with OpenCV: {e}")
        return False

def create_video_from_meme(meme, audio_path, output_path):
    """Create video from meme image and audio using OpenCV"""

    # Use OpenCV for video generation
    if OPENCV_AVAILABLE:
        return create_video_with_opencv(meme, audio_path, output_path)

    # Create placeholder if OpenCV not available
    logging.warning("OpenCV not available, creating placeholder")
    try:
        placeholder_path = output_path.replace('.mp4', '_placeholder.txt')
        with open(placeholder_path, 'w') as f:
            f.write(f"Video placeholder for meme: {meme.url}\n")
            f.write(f"Audio file: {audio_path}\n")
            f.write(f"Text: {meme.text}\n")
            f.write("Install OpenCV to generate actual videos.")
        logging.info(f"Created placeholder: {placeholder_path}")
        return True
    except Exception as e:
        logging.error(f"Error creating placeholder: {e}")
        return False
