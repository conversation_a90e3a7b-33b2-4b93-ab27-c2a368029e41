from flask import Flask
import os
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.DEBUG)

def create_app():
    """Create and configure Flask application"""
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'default_secret_key')

    # Update the database URI to use an absolute path
    base_dir = os.path.abspath(os.path.dirname(__file__))
    instance_dir = os.path.join(base_dir, "instance")

    # Ensure required directories exist
    os.makedirs(instance_dir, exist_ok=True)
    os.makedirs(os.path.join(base_dir, 'static', 'audio'), exist_ok=True)
    os.makedirs(os.path.join(base_dir, 'static', 'videos'), exist_ok=True)

    app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(instance_dir, "app.db")}'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Initialize extensions
    from models import db
    db.init_app(app)

    # Register blueprints
    from routes.auth import auth_bp
    from routes.dashboard import dashboard_bp
    from routes.memes import memes_bp
    from routes.config_routes import config_bp
    from routes.videos import videos_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(memes_bp)
    app.register_blueprint(config_bp)
    app.register_blueprint(videos_bp)

    # Create database tables
    with app.app_context():
        from models import db
        db.create_all()

    return app

# Create Flask application
app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
