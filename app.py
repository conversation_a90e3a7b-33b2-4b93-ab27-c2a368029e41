from flask import Flask, jsonify
import os
import secrets
from dotenv import load_dotenv
import logging
from logging.handlers import RotatingFileHandler

# Load environment variables
load_dotenv()


class Config:
    """Base configuration class"""
    SECRET_KEY = os.getenv('SECRET_KEY') or secrets.token_hex(32)
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Application settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file upload

    # Logging settings
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO').upper()
    LOG_FILE = os.getenv('LOG_FILE', 'app.log')
    LOG_MAX_SIZE = int(os.getenv('LOG_MAX_SIZE', 10 * 1024 * 1024))  # 10MB
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', 5))

    # Database settings
    DB_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', 10))
    DB_POOL_TIMEOUT = int(os.getenv('DB_POOL_TIMEOUT', 30))

    @staticmethod
    def init_app(app):
        """Initialize application with this config"""
        pass


class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True

    @classmethod
    def init_app(cls, app):
        Config.init_app(app)

        # Development-specific logging
        if not app.debug:
            return

        # Console logging for development
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        )
        console_handler.setFormatter(formatter)
        app.logger.addHandler(console_handler)
        app.logger.setLevel(logging.DEBUG)


class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False

    @classmethod
    def init_app(cls, app):
        Config.init_app(app)

        # Production logging to file
        if not os.path.exists('logs'):
            os.mkdir('logs')

        file_handler = RotatingFileHandler(
            f'logs/{cls.LOG_FILE}',
            maxBytes=cls.LOG_MAX_SIZE,
            backupCount=cls.LOG_BACKUP_COUNT
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, cls.LOG_LEVEL))
        app.logger.addHandler(file_handler)
        app.logger.setLevel(getattr(logging, cls.LOG_LEVEL))


class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    WTF_CSRF_ENABLED = False


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def create_app(config_name=None):
    """Create and configure Flask application"""
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')

    app = Flask(__name__)

    # Load configuration
    try:
        app.config.from_object(config[config_name])
        config[config_name].init_app(app)
        app.logger.info(f"Application started with {config_name} configuration")
    except KeyError:
        app.logger.error(f"Unknown configuration: {config_name}")
        app.config.from_object(config['default'])
        config['default'].init_app(app)
    except Exception as e:
        app.logger.error(f"Error loading configuration: {e}")
        raise

    # Setup database path
    try:
        base_dir = os.path.abspath(os.path.dirname(__file__))
        instance_dir = os.path.join(base_dir, "instance")

        app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{os.path.join(instance_dir, "app.db")}'

        # Ensure required directories exist with proper error handling
        directories = [
            instance_dir,
            os.path.join(base_dir, 'static', 'audio'),
            os.path.join(base_dir, 'static', 'videos'),
            os.path.join(base_dir, 'logs')
        ]

        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                app.logger.debug(f"Directory ensured: {directory}")
            except PermissionError:
                app.logger.error(f"Permission denied creating directory: {directory}")
                raise
            except Exception as e:
                app.logger.error(f"Error creating directory {directory}: {e}")
                raise

    except Exception as e:
        app.logger.error(f"Error setting up directories: {e}")
        raise

    # Initialize extensions with error handling
    try:
        from models import db
        db.init_app(app)
        app.logger.info("Database extension initialized successfully")
    except Exception as e:
        app.logger.error(f"Error initializing database: {e}")
        raise

    # Register blueprints with error handling
    blueprints = [
        ('routes.auth', 'auth_bp'),
        ('routes.dashboard', 'dashboard_bp'),
        ('routes.memes', 'memes_bp'),
        ('routes.config_routes', 'config_bp'),
        ('routes.videos', 'videos_bp')
    ]

    for module_name, blueprint_name in blueprints:
        try:
            module = __import__(module_name, fromlist=[blueprint_name])
            blueprint = getattr(module, blueprint_name)
            app.register_blueprint(blueprint)
            app.logger.debug(f"Registered blueprint: {blueprint_name}")
        except ImportError as e:
            app.logger.error(f"Error importing blueprint {blueprint_name}: {e}")
            raise
        except AttributeError as e:
            app.logger.error(f"Blueprint {blueprint_name} not found in {module_name}: {e}")
            raise
        except Exception as e:
            app.logger.error(f"Error registering blueprint {blueprint_name}: {e}")
            raise

    # Add health check endpoint
    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        try:
            # Check database connection
            with app.app_context():
                from models import db
                from sqlalchemy import text
                db.session.execute(text('SELECT 1'))

            return jsonify({
                'status': 'healthy',
                'timestamp': str(os.times()),
                'config': config_name
            }), 200
        except Exception as e:
            app.logger.error(f"Health check failed: {e}")
            return jsonify({
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': str(os.times())
            }), 500

    # Create database tables with error handling
    try:
        with app.app_context():
            from models import db
            db.create_all()
            app.logger.info("Database tables created successfully")
    except Exception as e:
        app.logger.error(f"Error creating database tables: {e}")
        raise

    app.logger.info("Application created successfully")
    return app

# Create Flask application
app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
