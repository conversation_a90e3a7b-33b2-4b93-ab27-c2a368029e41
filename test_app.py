"""
Test suite for the Flask application factory and configuration
"""
import unittest
import tempfile
import os
import shutil
from unittest.mock import patch, MagicMock
from app import create_app, Config, DevelopmentConfig, ProductionConfig, TestingConfig


class TestConfig(unittest.TestCase):
    """Test configuration classes"""

    def test_config_base_class(self):
        """Test base Config class"""
        self.assertIsNotNone(Config.SECRET_KEY)
        self.assertFalse(Config.SQLALCHEMY_TRACK_MODIFICATIONS)
        self.assertEqual(Config.MAX_CONTENT_LENGTH, 16 * 1024 * 1024)

    def test_development_config(self):
        """Test development configuration"""
        self.assertTrue(DevelopmentConfig.DEBUG)
        self.assertIsInstance(DevelopmentConfig.SECRET_KEY, str)

    def test_production_config(self):
        """Test production configuration"""
        self.assertFalse(ProductionConfig.DEBUG)

    def test_testing_config(self):
        """Test testing configuration"""
        self.assertTrue(TestingConfig.TESTING)
        self.assertFalse(TestingConfig.WTF_CSRF_ENABLED)


class TestAppFactory(unittest.TestCase):
    """Test Flask application factory"""

    def setUp(self):
        """Set up test environment"""
        self.test_dir = tempfile.mkdtemp()
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)

    def tearDown(self):
        """Clean up test environment"""
        os.chdir(self.original_cwd)
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_create_app_default_config(self):
        """Test app creation with default configuration"""
        with patch.dict(os.environ, {'FLASK_ENV': 'testing'}):
            app = create_app()
            self.assertIsNotNone(app)
            self.assertTrue(app.config['TESTING'])

    def test_create_app_development_config(self):
        """Test app creation with development configuration"""
        app = create_app('development')
        self.assertIsNotNone(app)
        self.assertTrue(app.config['DEBUG'])

    def test_create_app_production_config(self):
        """Test app creation with production configuration"""
        app = create_app('production')
        self.assertIsNotNone(app)
        self.assertFalse(app.config['DEBUG'])

    def test_create_app_testing_config(self):
        """Test app creation with testing configuration"""
        app = create_app('testing')
        self.assertIsNotNone(app)
        self.assertTrue(app.config['TESTING'])

    def test_create_app_invalid_config(self):
        """Test app creation with invalid configuration falls back to default"""
        app = create_app('invalid_config')
        self.assertIsNotNone(app)
        # Should fall back to default (development)
        self.assertTrue(app.config['DEBUG'])

    def test_directory_creation(self):
        """Test that required directories are created"""
        app = create_app('testing')

        # Check if directories exist
        base_dir = os.path.abspath(os.path.dirname(__file__))
        expected_dirs = [
            os.path.join(base_dir, 'instance'),
            os.path.join(base_dir, 'static', 'audio'),
            os.path.join(base_dir, 'static', 'videos'),
            os.path.join(base_dir, 'logs')
        ]

        for directory in expected_dirs:
            if os.path.exists(directory):
                self.assertTrue(os.path.isdir(directory))

    def test_database_configuration(self):
        """Test database configuration"""
        app = create_app('testing')
        self.assertIn('SQLALCHEMY_DATABASE_URI', app.config)
        self.assertTrue(app.config['SQLALCHEMY_DATABASE_URI'].startswith('sqlite:///'))

    def test_health_check_endpoint(self):
        """Test health check endpoint"""
        app = create_app('testing')

        with app.test_client() as client:
            response = client.get('/health')
            self.assertEqual(response.status_code, 200)

            data = response.get_json()
            self.assertIn('status', data)
            self.assertIn('timestamp', data)
            self.assertIn('config', data)

    @patch('app.db')
    def test_health_check_database_error(self, mock_db):
        """Test health check with database error"""
        mock_db.engine.execute.side_effect = Exception("Database error")

        app = create_app('testing')

        with app.test_client() as client:
            response = client.get('/health')
            self.assertEqual(response.status_code, 500)

            data = response.get_json()
            self.assertEqual(data['status'], 'unhealthy')
            self.assertIn('error', data)


class TestErrorHandling(unittest.TestCase):
    """Test error handling in application factory"""

    def test_permission_error_handling(self):
        """Test handling of permission errors during directory creation"""
        with patch('os.makedirs') as mock_makedirs:
            mock_makedirs.side_effect = PermissionError("Permission denied")

            with self.assertRaises(PermissionError):
                create_app('testing')

    @patch('app.__import__')
    def test_blueprint_import_error(self, mock_import):
        """Test handling of blueprint import errors"""
        mock_import.side_effect = ImportError("Module not found")

        with self.assertRaises(ImportError):
            create_app('testing')


if __name__ == '__main__':
    # Create a test suite
    test_suite = unittest.TestSuite()

    # Add test classes
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestConfig))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestAppFactory))
    test_suite.addTest(unittest.TestLoader().loadTestsFromTestCase(TestErrorHandling))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    # Print summary
    print(f"\nTests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")

    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")

    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
