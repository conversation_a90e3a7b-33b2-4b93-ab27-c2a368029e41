"""
Demo script to showcase the improvements made to app.py
"""
import os
import requests
import time
from app import create_app, config


def demo_configurations():
    """Demonstrate different configuration environments"""
    print("🔧 Configuration Demo")
    print("=" * 50)
    
    configs_to_test = ['development', 'production', 'testing']
    
    for config_name in configs_to_test:
        print(f"\n📋 Testing {config_name.upper()} configuration:")
        try:
            app = create_app(config_name)
            print(f"  ✅ App created successfully")
            print(f"  🐛 Debug mode: {app.config.get('DEBUG', False)}")
            print(f"  🧪 Testing mode: {app.config.get('TESTING', False)}")
            print(f"  🔐 Secret key configured: {'SECRET_KEY' in app.config}")
            print(f"  📊 Max content length: {app.config.get('MAX_CONTENT_LENGTH', 'Not set')}")
        except Exception as e:
            print(f"  ❌ Error: {e}")


def demo_health_check():
    """Demonstrate the health check endpoint"""
    print("\n\n🏥 Health Check Demo")
    print("=" * 50)
    
    try:
        app = create_app('testing')
        
        with app.test_client() as client:
            print("📡 Testing health check endpoint...")
            response = client.get('/health')
            
            print(f"  📊 Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"  ✅ Status: {data.get('status', 'Unknown')}")
                print(f"  ⏰ Timestamp: {data.get('timestamp', 'Unknown')}")
                print(f"  🔧 Config: {data.get('config', 'Unknown')}")
            else:
                print(f"  ❌ Health check failed")
                
    except Exception as e:
        print(f"  ❌ Error testing health check: {e}")


def demo_error_handling():
    """Demonstrate error handling capabilities"""
    print("\n\n🛡️ Error Handling Demo")
    print("=" * 50)
    
    print("🧪 Testing invalid configuration fallback...")
    try:
        app = create_app('invalid_config_name')
        print("  ✅ App created with fallback configuration")
        print(f"  🔧 Fallback to debug mode: {app.config.get('DEBUG', False)}")
    except Exception as e:
        print(f"  ❌ Error: {e}")


def demo_logging():
    """Demonstrate logging configuration"""
    print("\n\n📝 Logging Demo")
    print("=" * 50)
    
    try:
        app = create_app('development')
        
        with app.app_context():
            app.logger.info("This is an info message")
            app.logger.warning("This is a warning message")
            app.logger.debug("This is a debug message")
            
        print("  ✅ Logging messages sent successfully")
        print("  📁 Check console output for log messages")
        
        # Check if logs directory exists
        if os.path.exists('logs'):
            print("  📂 Logs directory created")
        else:
            print("  📂 Logs directory not created (normal for development)")
            
    except Exception as e:
        print(f"  ❌ Error testing logging: {e}")


def demo_directory_creation():
    """Demonstrate automatic directory creation"""
    print("\n\n📁 Directory Creation Demo")
    print("=" * 50)
    
    try:
        app = create_app('testing')
        
        base_dir = os.path.abspath(os.path.dirname(__file__))
        directories_to_check = [
            ('instance', os.path.join(base_dir, 'instance')),
            ('static/audio', os.path.join(base_dir, 'static', 'audio')),
            ('static/videos', os.path.join(base_dir, 'static', 'videos')),
            ('logs', os.path.join(base_dir, 'logs'))
        ]
        
        for name, path in directories_to_check:
            if os.path.exists(path):
                print(f"  ✅ {name}: Created successfully")
            else:
                print(f"  ⚠️ {name}: Not found (may not be created in test environment)")
                
    except Exception as e:
        print(f"  ❌ Error checking directories: {e}")


def demo_security_improvements():
    """Demonstrate security improvements"""
    print("\n\n🔐 Security Improvements Demo")
    print("=" * 50)
    
    try:
        app = create_app('production')
        
        # Check secret key
        secret_key = app.config.get('SECRET_KEY')
        if secret_key and secret_key != 'default_secret_key':
            print("  ✅ Strong secret key configured")
            print(f"  🔑 Key length: {len(secret_key)} characters")
        else:
            print("  ⚠️ Using default or weak secret key")
            
        # Check debug mode in production
        if not app.config.get('DEBUG', True):
            print("  ✅ Debug mode disabled in production")
        else:
            print("  ⚠️ Debug mode enabled (security risk)")
            
        # Check max content length
        max_size = app.config.get('MAX_CONTENT_LENGTH')
        if max_size:
            print(f"  ✅ File upload limit: {max_size / (1024*1024):.1f}MB")
        else:
            print("  ⚠️ No file upload limit set")
            
    except Exception as e:
        print(f"  ❌ Error checking security: {e}")


def main():
    """Run all demonstrations"""
    print("🚀 App.py Improvements Demonstration")
    print("=" * 60)
    print("This demo showcases all the improvements made to app.py:")
    print("• Security enhancements")
    print("• Configuration management")
    print("• Error handling")
    print("• Health check endpoint")
    print("• Logging improvements")
    print("• Directory management")
    print("=" * 60)
    
    # Run all demos
    demo_configurations()
    demo_health_check()
    demo_error_handling()
    demo_logging()
    demo_directory_creation()
    demo_security_improvements()
    
    print("\n\n🎉 Demo completed!")
    print("All improvements have been successfully implemented in app.py")


if __name__ == '__main__':
    main()
