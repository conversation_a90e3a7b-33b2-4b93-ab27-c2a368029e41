from flask import Blueprint, render_template, request, redirect, url_for, session
from models import Meme, Video, db
from services.helpers import require_login
from services.audio_service import generate_audio_from_text
from services.video_service import create_video_from_meme
import os
import uuid

videos_bp = Blueprint('videos', __name__)

@videos_bp.route('/generate_videos')
@require_login
def generate_videos():
    """Generate videos from memes"""
    user_id = session['user_id']
    memes = Meme.query.filter_by(user_id=user_id, discarded=False).all()

    generated_count = 0
    for meme in memes:
        if not meme.text or meme.text.strip() == "":
            continue

        try:
            # Generate unique filenames
            audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
            video_filename = f"video_{meme.id}_{uuid.uuid4().hex[:8]}.mp4"

            audio_path = os.path.join('static', 'audio', audio_filename)
            video_path = os.path.join('static', 'videos', video_filename)

            # Generate audio
            if generate_audio_from_text(meme.text, audio_path):
                # Generate video
                if create_video_from_meme(meme, audio_path, video_path):
                    # Save video record to database
                    video_record = Video(
                        user_id=user_id,
                        meme_id=meme.id,
                        video_path=video_path,
                        audio_path=audio_path,
                        text=meme.text
                    )
                    db.session.add(video_record)
                    generated_count += 1

        except Exception:
            continue

    db.session.commit()

    return redirect(url_for('videos.review_videos'))

@videos_bp.route('/review_audio')
@require_login
def review_audio():
    """Review generated audio files"""
    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()
    return render_template('review_audio.html', videos=videos)

@videos_bp.route('/review_videos')
@require_login
def review_videos():
    """Review generated videos"""
    user_id = session['user_id']
    videos = Video.query.filter_by(user_id=user_id, discarded=False).all()
    return render_template('review_videos.html', videos=videos)

@videos_bp.route('/discard_video', methods=['POST'])
@require_login
def discard_video():
    """Discard a video"""
    user_id = session['user_id']
    video_id = request.form.get('video_id')

    video = Video.query.filter_by(id=video_id, user_id=user_id).first()
    if video:
        video.discarded = True
        db.session.commit()

    return redirect(url_for('videos.review_videos'))
