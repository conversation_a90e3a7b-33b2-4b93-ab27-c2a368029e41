2025-05-29 14:36:12,981 INFO: Application started with production configuration [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:109]
2025-05-29 14:36:12,983 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:12,993 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:12,994 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
2025-05-29 14:36:12,997 INFO: Application started with testing configuration [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:109]
2025-05-29 14:36:12,999 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:13,010 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:13,010 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
2025-05-29 14:36:13,012 INFO: Application started with testing configuration [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:109]
2025-05-29 14:36:13,014 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:13,027 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:13,027 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
2025-05-29 14:36:13,044 ERROR: Unknown configuration: invalid_config_name [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:111]
2025-05-29 14:36:13,049 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:13,070 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:13,071 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
2025-05-29 14:36:13,077 INFO: Application started with development configuration [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:109]
2025-05-29 14:36:13,086 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:13,110 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:13,111 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
2025-05-29 14:36:13,112 INFO: This is an info message [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\demo_improvements.py:79]
2025-05-29 14:36:13,113 WARNING: This is a warning message [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\demo_improvements.py:80]
2025-05-29 14:36:13,115 INFO: Application started with testing configuration [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:109]
2025-05-29 14:36:13,120 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:13,137 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:13,142 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
2025-05-29 14:36:13,145 INFO: Application started with production configuration [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:109]
2025-05-29 14:36:13,145 INFO: Application started with production configuration [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:109]
2025-05-29 14:36:13,147 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:13,147 INFO: Database extension initialized successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:152]
2025-05-29 14:36:13,155 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:13,155 INFO: Database tables created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:211]
2025-05-29 14:36:13,159 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
2025-05-29 14:36:13,159 INFO: Application created successfully [in C:\Users\<USER>\Desktop\Automatic Meme Content Generator\app.py:216]
